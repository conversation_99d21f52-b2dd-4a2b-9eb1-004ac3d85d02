using UnityEngine;
using UnityEngine.UI;
using TMPro;

namespace HELLSTRIKE
{
    public class InteractionPromptUI : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private TextMeshProUGUI interactionText;
        [SerializeField] private string promptMessage = "E to Interact";
        
        [Header("Detection Settings")]
        [SerializeField] private float detectionRange = 3f;
        [SerializeField] private LayerMask playerLayer = -1;
        [SerializeField] private bool autoFindPlayer = true;
        [SerializeField] private bool debugPrompt = true;
        
        [Header("Animation")]
        [SerializeField] private bool enableAnimation = true;
        [SerializeField] private float animationDuration = 0.3f;
        [SerializeField] private AnimationCurve animationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);
        
        private Transform player;
        private bool isAnimating = false;
        private float animationTimer = 0f;
        private bool isVisible = false;
        private bool playerInRange = false;
        
        void Start()
        {
            // Auto-find player if enabled
            if (autoFindPlayer && player == null)
            {
                GameObject playerObj = GameObject.FindGameObjectWithTag("Player");
                if (playerObj != null)
                {
                    player = playerObj.transform;
                }
                else
                {
                    Debug.LogWarning("InteractionPromptUI: No Player found in scene!");
                }
            }
            
            // Hide prompt initially
            if (interactionText != null)
            {
                interactionText.gameObject.SetActive(false);
                isVisible = false;
            }
            
            if (debugPrompt)
            {
                Debug.Log("InteractionPromptUI: Initialized");
            }
        }
        
        void Update()
        {
            CheckPlayerProximity();
            HandleAnimation();
        }
        
        private void CheckPlayerProximity()
        {
            if (player == null || interactionText == null) return;
            
            // Calculate distance to player
            float distanceToPlayer = Vector3.Distance(transform.position, player.position);
            bool wasInRange = playerInRange;
            playerInRange = distanceToPlayer <= detectionRange;
            
            // Show/hide prompt based on proximity
            if (playerInRange && !isVisible)
            {
                ShowPrompt();
            }
            else if (!playerInRange && isVisible)
            {
                HidePrompt();
            }
            
            // Debug logging
            if (debugPrompt && wasInRange != playerInRange)
            {
                Debug.Log($"InteractionPromptUI: Player {(playerInRange ? "entered" : "left")} interaction range. Distance: {distanceToPlayer:F2}");
            }
        }
        
        private void ShowPrompt()
        {
            if (interactionText == null || isVisible) return;
            
            isVisible = true;
            interactionText.text = promptMessage;
            interactionText.gameObject.SetActive(true);
            
            if (enableAnimation)
            {
                TriggerShowAnimation();
            }
            
            if (debugPrompt)
            {
                Debug.Log("InteractionPromptUI: Showing interaction prompt");
            }
        }
        
        private void HidePrompt()
        {
            if (interactionText == null || !isVisible) return;
            
            isVisible = false;
            
            if (enableAnimation)
            {
                TriggerHideAnimation();
            }
            else
            {
                interactionText.gameObject.SetActive(false);
            }
            
            if (debugPrompt)
            {
                Debug.Log("InteractionPromptUI: Hiding interaction prompt");
            }
        }
        
        private void TriggerShowAnimation()
        {
            if (isAnimating) return;
            
            isAnimating = true;
            animationTimer = 0f;
            
            // Reset scale
            if (interactionText != null)
            {
                interactionText.transform.localScale = Vector3.zero;
            }
        }
        
        private void TriggerHideAnimation()
        {
            if (isAnimating) return;
            
            isAnimating = true;
            animationTimer = 0f;
        }
        
        private void HandleAnimation()
        {
            if (!isAnimating || !enableAnimation) return;
            
            animationTimer += Time.deltaTime;
            float progress = animationTimer / animationDuration;
            
            if (progress >= 1f)
            {
                // Animation complete
                isAnimating = false;
                animationTimer = 0f;
                
                if (isVisible)
                {
                    // Show animation complete
                    if (interactionText != null)
                    {
                        interactionText.transform.localScale = Vector3.one;
                    }
                }
                else
                {
                    // Hide animation complete
                    if (interactionText != null)
                    {
                        interactionText.gameObject.SetActive(false);
                        interactionText.transform.localScale = Vector3.one;
                    }
                }
                return;
            }
            
            // Apply animation curve
            float curveValue = animationCurve.Evaluate(progress);
            
            if (isVisible)
            {
                // Show animation - scale up
                float scale = curveValue;
                if (interactionText != null)
                {
                    interactionText.transform.localScale = Vector3.one * scale;
                }
            }
            else
            {
                // Hide animation - scale down
                float scale = 1f - curveValue;
                if (interactionText != null)
                {
                    interactionText.transform.localScale = Vector3.one * scale;
                }
            }
        }
        
        // Public methods for manual setup
        public void SetPlayer(Transform playerTransform)
        {
            player = playerTransform;
        }
        
        public void SetInteractionText(TextMeshProUGUI text)
        {
            interactionText = text;
        }
        
        public void SetPromptMessage(string message)
        {
            promptMessage = message;
            if (interactionText != null && isVisible)
            {
                interactionText.text = promptMessage;
            }
        }
        
        public void SetDetectionRange(float range)
        {
            detectionRange = range;
        }
        
        public void ForceShow()
        {
            ShowPrompt();
        }
        
        public void ForceHide()
        {
            HidePrompt();
        }
        
        // Properties
        public bool IsVisible => isVisible;
        public bool PlayerInRange => playerInRange;
        
        // Visual feedback in editor
        void OnDrawGizmosSelected()
        {
            // Draw detection range
            Gizmos.color = Color.cyan;
            Gizmos.DrawWireSphere(transform.position, detectionRange);
            
            // Draw line to player if in range
            if (player != null && playerInRange)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawLine(transform.position, player.position);
            }
        }
    }
}
