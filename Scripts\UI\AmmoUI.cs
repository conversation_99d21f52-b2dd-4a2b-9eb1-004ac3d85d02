using UnityEngine;
using UnityEngine.UI;

namespace HELLSTRIKE
{
    public class AmmoUI : MonoBehaviour
    {
        [Header("UI References")]
        [SerializeField] private Text ammoText;
        [SerializeField] private Text reloadText;
        [SerializeField] private Image[] bulletImages = new Image[6]; // Visual bullets for revolver
        [SerializeField] private Sprite fullBulletSprite;
        [SerializeField] private Sprite emptyBulletSprite;

        [Header("Settings")]
        [SerializeField] private bool autoFindGun = true;
        [SerializeField] private bool showReloadPrompt = true;
        [SerializeField] private Color lowAmmoColor = Color.red;
        [SerializeField] private Color normalAmmoColor = Color.white;
        [SerializeField] private int lowAmmoThreshold = 2;

        [Header("Animation")]
        [SerializeField] private bool enableAmmoAnimation = true;
        [SerializeField] private float animationDuration = 0.3f;
        [SerializeField] private AnimationCurve animationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

        private RevolverGun revolverGun;
        private bool isAnimating = false;
        private float animationTimer = 0f;
        private int lastAmmoCount = -1;
        
        void Start()
        {
            // Auto-find revolver gun if enabled
            if (autoFindGun && revolverGun == null)
            {
                revolverGun = FindFirstObjectByType<RevolverGun>();
                if (revolverGun == null)
                {
                    Debug.LogWarning("AmmoUI: No RevolverGun found in scene!");
                }
            }
            
            // Subscribe to gun events
            if (revolverGun != null)
            {
                revolverGun.OnAmmoChanged += UpdateAmmoDisplay;
                revolverGun.OnReload += OnReloadStarted;
                revolverGun.OnEmpty += OnGunEmpty;
                
                // Initialize display
                UpdateAmmoDisplay(revolverGun.CurrentAmmo, revolverGun.MaxAmmo);
            }
            
            // Hide reload text initially
            if (reloadText != null)
            {
                reloadText.gameObject.SetActive(false);
            }
        }
        
        void Update()
        {
            HandleAnimation();
            HandleReloadPrompt();
        }
        
        void OnDestroy()
        {
            // Unsubscribe from events
            if (revolverGun != null)
            {
                revolverGun.OnAmmoChanged -= UpdateAmmoDisplay;
                revolverGun.OnReload -= OnReloadStarted;
                revolverGun.OnEmpty -= OnGunEmpty;
            }
        }
        
        private void UpdateAmmoDisplay(int currentAmmo, int maxAmmo)
        {
            // Update text display
            if (ammoText != null)
            {
                ammoText.text = $"{currentAmmo}/{maxAmmo}";
                
                // Change color based on ammo count
                if (currentAmmo <= lowAmmoThreshold)
                {
                    ammoText.color = lowAmmoColor;
                }
                else
                {
                    ammoText.color = normalAmmoColor;
                }
            }
            
            // Update bullet images
            UpdateBulletImages(currentAmmo, maxAmmo);
            
            // Trigger animation if ammo changed
            if (enableAmmoAnimation && currentAmmo != lastAmmoCount && lastAmmoCount != -1)
            {
                TriggerAmmoAnimation();
            }
            
            lastAmmoCount = currentAmmo;
        }
        
        private void UpdateBulletImages(int currentAmmo, int maxAmmo)
        {
            for (int i = 0; i < bulletImages.Length; i++)
            {
                if (bulletImages[i] == null) continue;
                
                // Show/hide bullets based on max ammo
                if (i >= maxAmmo)
                {
                    bulletImages[i].gameObject.SetActive(false);
                    continue;
                }
                
                bulletImages[i].gameObject.SetActive(true);
                
                // Set sprite based on current ammo
                if (i < currentAmmo)
                {
                    bulletImages[i].sprite = fullBulletSprite;
                    bulletImages[i].color = Color.white;
                }
                else
                {
                    bulletImages[i].sprite = emptyBulletSprite;
                    bulletImages[i].color = Color.gray;
                }
            }
        }
        
        private void HandleReloadPrompt()
        {
            if (!showReloadPrompt || reloadText == null || revolverGun == null)
                return;
            
            // Show reload prompt when out of ammo and not reloading
            bool shouldShowReload = revolverGun.CurrentAmmo == 0 && !revolverGun.IsReloading();
            reloadText.gameObject.SetActive(shouldShowReload);
            
            if (shouldShowReload)
            {
                reloadText.text = "Press R to Reload";
            }
        }
        
        private void OnReloadStarted()
        {
            if (reloadText != null)
            {
                reloadText.gameObject.SetActive(true);
                reloadText.text = "Reloading...";
            }
        }
        
        private void OnGunEmpty()
        {
            // Could add additional effects here like screen flash, sound, etc.
            Debug.Log("AmmoUI: Gun is empty!");
        }
        
        private void TriggerAmmoAnimation()
        {
            if (isAnimating) return;
            
            isAnimating = true;
            animationTimer = 0f;
        }
        
        private void HandleAnimation()
        {
            if (!isAnimating || !enableAmmoAnimation) return;
            
            animationTimer += Time.deltaTime;
            float progress = animationTimer / animationDuration;
            
            if (progress >= 1f)
            {
                // Animation complete
                isAnimating = false;
                animationTimer = 0f;
                
                // Reset scale
                if (ammoText != null)
                {
                    ammoText.transform.localScale = Vector3.one;
                }
                return;
            }
            
            // Apply animation curve
            float curveValue = animationCurve.Evaluate(progress);
            float scale = 1f + (curveValue * 0.2f); // Scale up by 20% max
            
            if (ammoText != null)
            {
                ammoText.transform.localScale = Vector3.one * scale;
            }
        }
        
        // Public methods for manual setup
        public void SetRevolverGun(RevolverGun gun)
        {
            // Unsubscribe from old gun
            if (revolverGun != null)
            {
                revolverGun.OnAmmoChanged -= UpdateAmmoDisplay;
                revolverGun.OnReload -= OnReloadStarted;
                revolverGun.OnEmpty -= OnGunEmpty;
            }
            
            // Subscribe to new gun
            revolverGun = gun;
            if (revolverGun != null)
            {
                revolverGun.OnAmmoChanged += UpdateAmmoDisplay;
                revolverGun.OnReload += OnReloadStarted;
                revolverGun.OnEmpty += OnGunEmpty;
                
                // Update display immediately
                UpdateAmmoDisplay(revolverGun.CurrentAmmo, revolverGun.MaxAmmo);
            }
        }
        
        public void SetAmmoText(Text text)
        {
            ammoText = text;
        }
        
        public void SetReloadText(Text text)
        {
            reloadText = text;
        }
        
        public void SetBulletImages(Image[] images)
        {
            bulletImages = images;
        }
    }
}
